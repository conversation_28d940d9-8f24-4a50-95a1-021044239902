import 'package:json_annotation/json_annotation.dart';
import 'package:octasync_client/models/role_mgmt/employee_department.dart';

part 'employee.g.dart';

/// 员工信息模型
@JsonSerializable()
class Employee {
  /// 员工ID
  @Json<PERSON><PERSON>(name: 'EmployeeId')
  String? employeeId;

  /// 姓名
  @Json<PERSON><PERSON>(name: 'Name', defaultValue: '')
  String name;

  /// 头像附件ID
  @Json<PERSON><PERSON>(name: 'AvatarId')
  String? avatarId;

  /// 头像地址
  @<PERSON><PERSON><PERSON><PERSON>(name: 'Avatar', defaultValue: '')
  String avatar;

  /// 工号
  @J<PERSON><PERSON><PERSON>(name: 'Number', defaultValue: '')
  String number;

  /// 性别枚举 1.男 2.女 3.保密
  @Json<PERSON><PERSON>(name: 'Sexenum', defaultValue: null)
  int? sexEnum;

  /// 手机号码
  @Json<PERSON>ey(name: 'PhoneNumber', defaultValue: '')
  String phoneNumber;

  /// 手机号码区号
  @<PERSON><PERSON><PERSON><PERSON>(name: 'PhoneAreaCode', defaultValue: '+86')
  String phoneAreaCode;

  /// 工作邮箱
  @J<PERSON><PERSON><PERSON>(name: 'Email', defaultValue: '')
  String email;

  /// 人员类型枚举 1.正式 2.实习 3.外包 4.劳务 5.顾问
  @JsonKey(name: 'EmployeeTypeenum', defaultValue: null)
  int? employeeTypeEnum;

  /// 固定电话
  @JsonKey(name: 'FixedPhoneNumber', defaultValue: '')
  String fixedPhoneNumber;

  /// 微信号
  @JsonKey(name: 'WxNumber', defaultValue: '')
  String wxNumber;

  /// 直属上级员工ID
  @JsonKey(name: 'SuperiorEmployeeId')
  String? superiorEmployeeId;

  /// 状态枚举 1.启用 2.停用 3.离职
  @JsonKey(name: 'Stateenum', defaultValue: null)
  int? stateEnum;

  /// 离职时间
  @JsonKey(name: 'DateLeft')
  DateTime? dateLeft;

  /// 离职交接
  @JsonKey(name: 'Handover')
  bool? handover;

  /// 人员部门
  @JsonKey(name: 'DepartmentList', defaultValue: [])
  List<EmployeeDepartment>? departmentList;

  Employee({
    this.employeeId,
    this.name = '',
    this.avatar = '',
    this.number = '',
    this.sexEnum,
    this.phoneNumber = '',
    this.phoneAreaCode = '+86',
    this.email = '',
    this.avatarId,
    this.employeeTypeEnum,
    this.fixedPhoneNumber = '',
    this.wxNumber = '',
    this.superiorEmployeeId,
    this.stateEnum,
    this.dateLeft,
    this.handover,
    this.departmentList = const [],
  });

  factory Employee.fromJson(Map<String, dynamic> json) => _$EmployeeFromJson(json);

  Map<String, dynamic> toJson() => _$EmployeeToJson(this);

  /// 获取性别文本
  String get sexText {
    switch (sexEnum) {
      case 1:
        return '男';
      case 2:
        return '女';
      case 3:
        return '保密';
      default:
        return '未知';
    }
  }

  /// 获取员工类型文本
  String get employeeTypeText {
    switch (employeeTypeEnum) {
      case 1:
        return '正式';
      case 2:
        return '实习';
      case 3:
        return '外包';
      case 4:
        return '劳务';
      case 5:
        return '顾问';
      default:
        return '未知';
    }
  }

  /// 获取状态文本
  String get stateText {
    switch (stateEnum) {
      case 1:
        return '启用';
      case 2:
        return '停用';
      case 3:
        return '离职';
      default:
        return '未知';
    }
  }

  /// 是否已离职
  bool get isLeft => stateEnum == 3;

  /// 是否启用状态
  bool get isActive => stateEnum == 1;
}
